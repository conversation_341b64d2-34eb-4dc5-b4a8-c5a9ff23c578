
export enum UserRole {
  ARTIST = 'Artist',
  EDUCATOR = 'Educator',
  FAN = 'Fan',
  ADMIN = 'Admin',
}

export interface SocialLinks {
  instagram?: string;
  tiktok?: string;
  youtube?: string;
  soundcloud?: string;
  spotify?: string;
}

export interface User {
  id: string;
  email: string;
  password?: string; // Not stored long-term, only for signup/login
  role: UserRole;
  displayName: string;
  profilePhotoUrl: string;
  bannerImageUrl: string;
  location?: string;
  bio?: string;
  tags: string[];
  socialLinks: SocialLinks;
  // Artist specific
  artistStatement?: string;
  genre?: string[];
  // Educator specific
  lessonsOffered?: Lesson[];
  // Fan specific
  followedArtistIds?: string[];
  savedPostIds?: string[];
  rsvpedEventIds?: string[];
  // Admin specific
  isActive?: boolean;
  lastLoginAt?: string;
  createdAt?: string;
}

export enum PostCategory {
  MUSIC = 'Music',
  VISUAL_ART = 'Visual Art',
  PERFORMANCE = 'Performance',
  DANCE = 'Dance',
  OTHER = 'Other',
}

export interface Post {
  id: string;
  userId: string; // Artist's ID
  title: string;
  description: string;
  mediaUrl: string; // URL to image, or embed code for YouTube/SoundCloud
  mediaType: 'image' | 'youtube' | 'soundcloud';
  category: PostCategory;
  tags: string[];
  likes: number;
  // comments: Comment[]; // Simplified for MVP
  createdAt: string; // ISO Date string
}

export interface Event {
  id: string;
  educatorId: string;
  title: string;
  description: string;
  dateTime: string; // ISO Date string
  format: 'virtual' | 'in-person';
  locationOrLink: string;
  price?: string;
  rsvps: string[]; // Array of user IDs
  createdAt: string; // ISO Date string
}

export interface Lesson {
  id: string;
  title: string;
  description: string;
  duration?: string; // e.g., "60 minutes"
  price?: string;
}

// For context
export interface AppContextType {
  currentUser: User | null;
  users: User[];
  posts: Post[];
  events: Event[];
  login: (email: string, password: string) => Promise<User | null>;
  signup: (userData: Omit<User, 'id' | 'profilePhotoUrl' | 'bannerImageUrl' | 'tags' | 'socialLinks'> & Partial<Pick<User, 'tags' | 'socialLinks'>>) => Promise<User | null>;
  logout: () => void;
  updateUserProfile: (userId: string, data: Partial<User>) => Promise<User | null>;
  getUserById: (userId: string) => User | null;
  createPost: (postData: Omit<Post, 'id' | 'likes' | 'createdAt'>) => Promise<Post | null>;
  getPosts: (filters?: { category?: PostCategory; userId?: string }) => Post[];
  createEvent: (eventData: Omit<Event, 'id' | 'rsvps' | 'createdAt'>) => Promise<Event | null>;
  getEvents: (filters?: { educatorId?: string }) => Event[];
  rsvpToEvent: (eventId: string, userId: string) => Promise<boolean>;
  followArtist: (fanId: string, artistId: string) => Promise<boolean>;
  savePost: (fanId: string, postId: string) => Promise<boolean>;
  // Admin functions
  deleteUser: (userId: string) => Promise<boolean>;
  updateUserRole: (userId: string, newRole: UserRole) => Promise<boolean>;
  toggleUserStatus: (userId: string) => Promise<boolean>;
  deletePost: (postId: string) => Promise<boolean>;
  deleteEvent: (eventId: string) => Promise<boolean>;
  getUserStats: () => { totalUsers: number; activeUsers: number; usersByRole: Record<UserRole, number> };
  resetToInitialData: () => void;
}

export type IconName =
  | 'user' | 'users' | 'photo' | 'musicalNote' | 'videoCamera' | 'academicCap'
  | 'calendarDays' | 'mapPin' | 'link' | 'heart' | 'chatBubbleLeft' | 'share'
  | 'plusCircle' | 'pencil' | 'trash' | 'magnifyingGlass' | 'adjustmentsHorizontal'
  | 'home' | 'bolt' | 'calendar' | 'userCircle' | 'arrowLeftOnRectangle' | 'arrowRightOnRectangle'
  | 'eye' | 'tag' | 'cog6Tooth' | 'shield' | 'chartBar' | 'userGroup' | 'exclamationTriangle';
