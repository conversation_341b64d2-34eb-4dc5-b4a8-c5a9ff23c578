
import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Layout } from './components/Layout';
import HomePage from './pages/HomePage';
import AuthPage from './pages/AuthPage';
import UserDashboardPage from './pages/UserDashboardPage';
import AdminDashboardPage from './pages/AdminDashboardPage';
import ExplorePage from './pages/ExplorePage';
import PublicProfilePage from './pages/PublicProfilePage';
import { useApp } from './AppContext';
import { UserRole } from './types';

const App: React.FC = () => {
  const { currentUser } = useApp();

  return (
    <Layout>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/auth" element={currentUser ? <Navigate to="/dashboard" /> : <AuthPage />} />
        <Route path="/dashboard" element={currentUser ? <UserDashboardPage /> : <Navigate to="/auth" />} />
        <Route path="/admin" element={
          currentUser && currentUser.role === UserRole.ADMIN ?
            <AdminDashboardPage /> :
            <Navigate to="/dashboard" />
        } />
        <Route path="/explore" element={<ExplorePage tab="feed" />} />
        <Route path="/events" element={<ExplorePage tab="events" />} />
        <Route path="/artist/:userId" element={<PublicProfilePage />} />
        <Route path="*" element={<Navigate to="/" />} /> {/* Basic 404 redirect to home */}
      </Routes>
    </Layout>
  );
};

export default App;
