
import React, { useState, useMemo } from 'react';
import { PageContainer } from '../components/Layout';
import { FeedItem, EventItem, Button, Input, Select, Icon } from '../components';
import { useApp } from '../AppContext';
import { Post, Event, PostCategory, User } from '../types';

interface ExplorePageProps {
  tab?: 'feed' | 'events';
}

const ExplorePage: React.FC<ExplorePageProps> = ({ tab = 'feed' }) => {
  const { getPosts, getEvents, getUserById, users } = useApp();
  const [activeTab, setActiveTab] = useState<'feed' | 'events'>(tab);

  // Filters for Feed
  const [searchTermFeed, setSearchTermFeed] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<PostCategory | ''>('');
  const [artistFilterFeed, setArtistFilterFeed] = useState<string>(''); // User ID

  // Filters for Events
  const [searchTermEvents, setSearchTermEvents] = useState('');
  const [educatorFilterEvents, setEducatorFilterEvents] = useState<string>(''); // User ID
  const [eventFormatFilter, setEventFormatFilter] = useState<'virtual' | 'in-person' | ''>('');


  const artists = useMemo(() => users.filter(u => u.role === 'Artist'), [users]);
  const educators = useMemo(() => users.filter(u => u.role === 'Educator'), [users]);

  const filteredPosts = useMemo(() => {
    return getPosts()
      .filter(post =>
        (post.title.toLowerCase().includes(searchTermFeed.toLowerCase()) ||
         post.description.toLowerCase().includes(searchTermFeed.toLowerCase()) ||
         post.tags.some(tag => tag.toLowerCase().includes(searchTermFeed.toLowerCase()))) &&
        (categoryFilter ? post.category === categoryFilter : true) &&
        (artistFilterFeed ? post.userId === artistFilterFeed : true)
      );
  }, [getPosts, searchTermFeed, categoryFilter, artistFilterFeed]);

  const filteredEvents = useMemo(() => {
    return getEvents()
      .filter(event =>
        (event.title.toLowerCase().includes(searchTermEvents.toLowerCase()) ||
         event.description.toLowerCase().includes(searchTermEvents.toLowerCase())) &&
        (educatorFilterEvents ? event.educatorId === educatorFilterEvents : true) &&
        (eventFormatFilter ? event.format === eventFormatFilter : true)
      );
  }, [getEvents, searchTermEvents, educatorFilterEvents, eventFormatFilter]);

  const TabButton: React.FC<{label: string, isActive: boolean, onClick: () => void}> = ({label, isActive, onClick}) => (
     <Button
        variant={isActive ? 'primary' : 'ghost'}
        onClick={onClick}
        size="lg"
        className={`transform hover:scale-105 transition-all duration-300 shadow-lg ${
          isActive
            ? 'bg-gradient-to-r from-primary to-secondary text-white shadow-primary/50'
            : 'text-neutral-dark hover:text-primary hover:bg-primary/10 border border-neutral-light hover:border-primary/30'
        }`}
      >
        {label}
      </Button>
  );

  return (
    <PageContainer title="Explore">
      <div className="w-full max-w-7xl mx-auto">
        {/* Tab Navigation */}
        <div className="mb-8 flex flex-col sm:flex-row justify-center border-b border-neutral-light pb-4">
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
            <TabButton
              label="🎨 Creative Feed"
              isActive={activeTab === 'feed'}
              onClick={() => setActiveTab('feed')}
            />
            <TabButton
              label="📅 Events & Workshops"
              isActive={activeTab === 'events'}
              onClick={() => setActiveTab('events')}
            />
          </div>
        </div>

        {activeTab === 'feed' && (
          <div className="w-full">
            <div className="text-center mb-8">
              <h2 className="text-3xl md:text-4xl font-bold text-neutral-darkest mb-4">
                <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                  Discover Art & Music
                </span>
              </h2>
              <div className="w-24 h-1 bg-gradient-to-r from-primary to-secondary mx-auto rounded-full"></div>
            </div>

            {/* Filters */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-8 p-6 bg-gradient-to-br from-neutral-lightest to-white rounded-2xl shadow-lg border border-neutral-light/50">
              <Input
                placeholder="🔍 Search posts..."
                value={searchTermFeed}
                onChange={e => setSearchTermFeed(e.target.value)}
                icon={<Icon name="magnifyingGlass" className="w-5 h-5 text-neutral-dark" />}
                className="transition-all duration-300 focus:ring-2 focus:ring-primary/50"
              />
              <Select
                value={categoryFilter}
                onChange={e => setCategoryFilter(e.target.value as PostCategory | '')}
                options={[{value: '', label: '🎭 All Categories'}, ...Object.values(PostCategory).map(c => ({value: c, label: c}))]}
                className="transition-all duration-300 focus:ring-2 focus:ring-primary/50"
              />
              <Select
                value={artistFilterFeed}
                onChange={e => setArtistFilterFeed(e.target.value)}
                options={[{value: '', label: '👨‍🎨 All Artists'}, ...artists.map(a => ({value: a.id, label: a.displayName}))]}
                className="transition-all duration-300 focus:ring-2 focus:ring-primary/50"
              />
            </div>

            {/* Posts Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 w-full">
              {filteredPosts.length > 0 ? filteredPosts.map((post, index) => (
                <div key={post.id} className={`animate-fade-in-up animation-delay-${(index % 3) * 200}`}>
                  <FeedItem post={post} author={getUserById(post.userId) || undefined} />
                </div>
              )) : (
                <div className="col-span-full text-center py-16">
                  <div className="text-6xl mb-4 animate-bounce">🎨</div>
                  <p className="text-xl text-neutral-dark mb-2">No posts match your criteria</p>
                  <p className="text-neutral-dark">Try broadening your search!</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'events' && (
          <div className="w-full">
            <div className="text-center mb-8">
              <h2 className="text-3xl md:text-4xl font-bold text-neutral-darkest mb-4">
                <span className="bg-gradient-to-r from-secondary to-primary bg-clip-text text-transparent">
                  Find Events & Workshops
                </span>
              </h2>
              <div className="w-24 h-1 bg-gradient-to-r from-secondary to-primary mx-auto rounded-full"></div>
            </div>

            {/* Filters */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-8 p-6 bg-gradient-to-br from-neutral-lightest to-white rounded-2xl shadow-lg border border-neutral-light/50">
              <Input
                placeholder="🔍 Search events..."
                value={searchTermEvents}
                onChange={e => setSearchTermEvents(e.target.value)}
                icon={<Icon name="magnifyingGlass" className="w-5 h-5 text-neutral-dark" />}
                className="transition-all duration-300 focus:ring-2 focus:ring-secondary/50"
              />
              <Select
                value={educatorFilterEvents}
                onChange={e => setEducatorFilterEvents(e.target.value)}
                options={[{value: '', label: '🎓 All Educators'}, ...educators.map(e => ({value: e.id, label: e.displayName}))]}
                className="transition-all duration-300 focus:ring-2 focus:ring-secondary/50"
              />
              <Select
                value={eventFormatFilter}
                onChange={e => setEventFormatFilter(e.target.value as 'virtual' | 'in-person' | '')}
                options={[
                  {value: '', label: '🌐 All Formats'},
                  {value: 'virtual', label: '💻 Virtual'},
                  {value: 'in-person', label: '📍 In-Person'}
                ]}
                className="transition-all duration-300 focus:ring-2 focus:ring-secondary/50"
              />
            </div>

            {/* Events Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 w-full">
              {filteredEvents.length > 0 ? filteredEvents.map((event, index) => (
                <div key={event.id} className={`animate-fade-in-up animation-delay-${(index % 2) * 200}`}>
                  <EventItem event={event} educator={getUserById(event.educatorId) || undefined} />
                </div>
              )) : (
                <div className="col-span-full text-center py-16">
                  <div className="text-6xl mb-4 animate-bounce">📅</div>
                  <p className="text-xl text-neutral-dark mb-2">No events match your criteria</p>
                  <p className="text-neutral-dark">Check back soon for new workshops!</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </PageContainer>
  );
};

export default ExplorePage;
