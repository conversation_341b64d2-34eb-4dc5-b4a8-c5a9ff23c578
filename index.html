<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>The MusicArt Club</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/heroicons/2.1.3/24/outline/styles.min.css" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: { // Orange
              light: '#fbbf24', // orange-400
              DEFAULT: '#f97316', // orange-500
              dark: '#ea580c', // orange-600
            },
            secondary: { // Deep Red/Maroon
              light: '#dc2626', // red-600
              DEFAULT: '#b91c1c', // red-700
              dark: '#991b1b', // red-800
            },
            accent: { // Yellow
              light: '#fde047', // yellow-300
              DEFAULT: '#facc15', // yellow-400
              dark: '#eab308', // yellow-500
            },
            neutral: {
              lightest: '#f8fafc', // slate-50
              light: '#f1f5f9',    // slate-100
              DEFAULT: '#64748b', // slate-500
              dark: '#334155',    // slate-700
              darkest: '#0f172a'  // slate-900
            }
          },
          fontFamily: {
            sans: ['Inter', 'sans-serif'],
          },
        }
      }
    }
  </script>
  <style>
    /* For Heroicons compatibility if needed, or simple icon styling */
    .icon {
      width: 1.5rem;
      height: 1.5rem;
      display: inline-block;
      vertical-align: middle;
    }
    body {
      background-color: #f8fafc; /* neutral-lightest */
    }

    /* Custom Animations for Music/Art Theme */
    @keyframes float {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-20px) rotate(5deg); }
    }

    @keyframes floatReverse {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(20px) rotate(-5deg); }
    }

    @keyframes shimmer {
      0% { background-position: -200% 0; }
      100% { background-position: 200% 0; }
    }

    @keyframes gradientShift {
      0%, 100% { opacity: 0.3; }
      50% { opacity: 0.7; }
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes bounceIn {
      0% {
        opacity: 0;
        transform: scale(0.3) translateY(50px);
      }
      50% {
        opacity: 1;
        transform: scale(1.05) translateY(-10px);
      }
      70% {
        transform: scale(0.9) translateY(0);
      }
      100% {
        opacity: 1;
        transform: scale(1) translateY(0);
      }
    }

    @keyframes expand {
      from {
        width: 0;
      }
      to {
        width: 6rem;
      }
    }

    @keyframes bounceSlow {
      0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
      }
      40%, 43% {
        transform: translate3d(0, -15px, 0);
      }
      70% {
        transform: translate3d(0, -7px, 0);
      }
      90% {
        transform: translate3d(0, -2px, 0);
      }
    }

    /* Floating Musical Notes */
    .floating-notes {
      position: absolute;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    .note {
      position: absolute;
      font-size: 2rem;
      color: rgba(249, 115, 22, 0.1); /* primary with opacity */
      animation: float 6s ease-in-out infinite;
      pointer-events: none;
    }

    .note-1 {
      left: 10%;
      top: 20%;
      animation-delay: 0s;
      animation-duration: 8s;
    }

    .note-2 {
      left: 80%;
      top: 30%;
      animation-delay: 2s;
      animation-duration: 6s;
    }

    .note-3 {
      left: 60%;
      top: 70%;
      animation-delay: 4s;
      animation-duration: 7s;
    }

    .note-4 {
      left: 20%;
      top: 80%;
      animation-delay: 1s;
      animation-duration: 9s;
    }

    .note-5 {
      left: 90%;
      top: 60%;
      animation-delay: 3s;
      animation-duration: 5s;
    }

    .note-6 {
      left: 40%;
      top: 10%;
      animation-delay: 5s;
      animation-duration: 8s;
    }

    /* Paint Splashes */
    .paint-splashes {
      position: absolute;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    .splash {
      position: absolute;
      border-radius: 50%;
      animation: floatReverse 10s ease-in-out infinite;
      pointer-events: none;
    }

    .splash-1 {
      width: 60px;
      height: 60px;
      background: radial-gradient(circle, rgba(185, 28, 28, 0.1) 0%, transparent 70%);
      left: 15%;
      top: 40%;
      animation-delay: 0s;
    }

    .splash-2 {
      width: 80px;
      height: 80px;
      background: radial-gradient(circle, rgba(250, 204, 21, 0.1) 0%, transparent 70%);
      right: 20%;
      top: 60%;
      animation-delay: 3s;
    }

    .splash-3 {
      width: 40px;
      height: 40px;
      background: radial-gradient(circle, rgba(249, 115, 22, 0.1) 0%, transparent 70%);
      left: 70%;
      bottom: 30%;
      animation-delay: 6s;
    }

    /* Floating Geometric Shapes */
    .floating-shape {
      position: absolute;
      animation: float 8s ease-in-out infinite;
      pointer-events: none;
    }

    .shape-1 {
      width: 20px;
      height: 20px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      left: 20%;
      top: 30%;
      animation-delay: 0s;
    }

    .shape-2 {
      width: 15px;
      height: 15px;
      background: rgba(255, 255, 255, 0.1);
      transform: rotate(45deg);
      right: 30%;
      top: 20%;
      animation-delay: 2s;
    }

    .shape-3 {
      width: 25px;
      height: 25px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 30%;
      left: 80%;
      bottom: 40%;
      animation-delay: 4s;
    }

    .shape-4 {
      width: 18px;
      height: 18px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      left: 10%;
      bottom: 20%;
      animation-delay: 6s;
    }

    /* Animation Classes */
    .animate-shimmer {
      animation: shimmer 3s ease-in-out infinite;
    }

    .animate-gradient-shift {
      animation: gradientShift 4s ease-in-out infinite;
    }

    .animate-fade-in-up {
      animation: fadeInUp 0.8s ease-out forwards;
    }

    .animate-bounce-in {
      animation: bounceIn 0.8s ease-out forwards;
    }

    .animate-expand {
      animation: expand 1s ease-out forwards;
    }

    .animate-bounce-slow {
      animation: bounceSlow 3s ease-in-out infinite;
    }

    /* Animation Delays */
    .animation-delay-200 {
      animation-delay: 0.2s;
    }

    .animation-delay-400 {
      animation-delay: 0.4s;
    }

    .animation-delay-600 {
      animation-delay: 0.6s;
    }

    /* Background Size for Shimmer Effect */
    .bg-300\% {
      background-size: 300% 300%;
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
      .note {
        font-size: 1.5rem;
      }

      .splash {
        transform: scale(0.7);
      }

      .floating-shape {
        transform: scale(0.8);
      }
    }
  </style>
<script type="importmap">
{
  "imports": {
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.1",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0"
  }
}
</script>
</head>
<body class="bg-neutral-lightest text-neutral-darkest font-sans antialiased">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html><link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
