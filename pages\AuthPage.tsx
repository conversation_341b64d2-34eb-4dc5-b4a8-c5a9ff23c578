import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button, Input, Select } from '../components';
import { PageContainer } from '../components/Layout';
import { useApp } from '../AppContext';
import { UserRole } from '../types';

const AuthPage: React.FC = () => {
  const { login, signup, currentUser, resetToInitialData } = useApp();
  const navigate = useNavigate();
  const location = useLocation();

  const queryParams = new URLSearchParams(location.search);
  const initialAction = queryParams.get('action') === 'signup' ? 'signup' : 'login';
  const initialRole = queryParams.get('role') as UserRole || UserRole.FAN;

  const [isLoginView, setIsLoginView] = useState(initialAction === 'login');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [role, setRole] = useState<UserRole>(initialRole);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (currentUser) {
      navigate('/dashboard');
    }
  }, [currentUser, navigate]);

  useEffect(() => {
    setIsLoginView(initialAction === 'login');
    setRole(initialRole);
  }, [location.search, initialAction, initialRole]);


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);
    try {
      if (isLoginView) {
        const user = await login(email, password);
        if (user) navigate('/dashboard');
        else setError('Login failed. Please check your credentials.');
      } else {
        if (!displayName) {
          setError('Display name is required for signup.');
          setIsLoading(false);
          return;
        }
        const user = await signup({ email, password, displayName, role });
        if (user) navigate('/dashboard');
        else setError('Signup failed. User might already exist or invalid data.');
      }
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred.');
    } finally {
      setIsLoading(false);
    }
  };

  const testAccounts = [
    { email: '<EMAIL>', password: 'admin123', role: 'Admin', name: 'Admin User' },
    { email: '<EMAIL>', password: 'password', role: 'Artist', name: 'Artistic Annie' },
    { email: '<EMAIL>', password: 'password', role: 'Educator', name: 'Musical Mark' },
    { email: '<EMAIL>', password: 'password', role: 'Fan', name: 'Creative Chris' }
  ];

  const handleTestLogin = async (testAccount: typeof testAccounts[0]) => {
    setError('');
    setIsLoading(true);
    try {
      const user = await login(testAccount.email, testAccount.password);
      if (user) navigate('/dashboard');
      else setError('Test login failed.');
    } catch (err: any) {
      setError(err.message || 'Test login failed.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <PageContainer title={isLoginView ? 'Login to Your Account' : 'Create an Account'}>
      <div className="w-full max-w-4xl mx-auto mt-8 px-4">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">

          {/* Main Auth Form */}
          <div className="lg:col-span-2">
            <div className="bg-white p-8 shadow-xl rounded-2xl border border-neutral-light">
              <form onSubmit={handleSubmit} className="space-y-6">
                <Input
                  label="Email Address"
                  type="email"
                  name="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  placeholder="<EMAIL>"
                />
                <Input
                  label="Password"
                  type="password"
                  name="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  placeholder="••••••••"
                />
                {!isLoginView && (
                  <>
                    <Input
                      label="Display Name"
                      type="text"
                      name="displayName"
                      value={displayName}
                      onChange={(e) => setDisplayName(e.target.value)}
                      required
                      placeholder="Your Name or Stage Name"
                    />
                    <Select
                      label="I am a..."
                      name="role"
                      value={role}
                      onChange={(e) => setRole(e.target.value as UserRole)}
                      options={Object.values(UserRole).map(r => ({ value: r, label: r }))}
                      required
                    />
                  </>
                )}
                {error && <p className="text-red-500 text-sm">{error}</p>}
                <Button type="submit" variant="primary" className="w-full transform hover:scale-105 transition-all duration-300" isLoading={isLoading}>
                  {isLoginView ? 'Login' : 'Sign Up'}
                </Button>
              </form>
              <p className="mt-6 text-center text-sm text-neutral-dark">
                {isLoginView ? "Don't have an account? " : 'Already have an account? '}
                <button
                  onClick={() => { setIsLoginView(!isLoginView); setError(''); }}
                  className="font-medium text-primary hover:text-primary-dark transition-colors"
                >
                  {isLoginView ? 'Sign Up' : 'Login'}
                </button>
              </p>
            </div>
          </div>

          {/* Test Login Panel */}
          {isLoginView && (
            <div className="lg:col-span-1">
              <div className="bg-gradient-to-br from-primary/5 to-secondary/5 p-6 rounded-2xl border border-primary/20">
                <h3 className="text-lg font-semibold text-neutral-darkest mb-4 text-center">
                  🧪 Quick Test Login
                </h3>
                <p className="text-sm text-neutral-dark mb-6 text-center">
                  For development & testing purposes
                </p>

                <div className="space-y-3">
                  {testAccounts.map((account, index) => (
                    <button
                      key={account.email}
                      onClick={() => handleTestLogin(account)}
                      disabled={isLoading}
                      className={`w-full p-3 rounded-lg text-left transition-all duration-300 transform hover:scale-105 hover:shadow-lg border-2 ${
                        account.role === 'Artist'
                          ? 'bg-primary/10 border-primary/30 hover:bg-primary/20 text-primary-dark'
                          : account.role === 'Educator'
                          ? 'bg-secondary/10 border-secondary/30 hover:bg-secondary/20 text-secondary-dark'
                          : account.role === 'Admin'
                          ? 'bg-red-50 border-red-300 hover:bg-red-100 text-red-800'
                          : 'bg-accent/10 border-accent/30 hover:bg-accent/20 text-accent-dark'
                      } ${isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-sm">{account.name}</div>
                          <div className="text-xs opacity-75">{account.role}</div>
                        </div>
                        <div className="text-lg">
                          {account.role === 'Artist' ? '🎨' : account.role === 'Educator' ? '🎓' : account.role === 'Admin' ? '🛡️' : '❤️'}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>

                <div className="mt-4 p-3 bg-neutral-light/50 rounded-lg">
                  <p className="text-xs text-neutral-dark text-center mb-2">
                    💡 These buttons auto-fill credentials for testing different user roles
                  </p>
                  <button
                    onClick={() => {
                      resetToInitialData();
                      alert('Data reset! Admin account is now available.');
                    }}
                    className="w-full mt-2 px-3 py-1 bg-yellow-100 border border-yellow-300 rounded text-xs text-yellow-800 hover:bg-yellow-200 transition-colors"
                  >
                    🔄 Reset Data (if admin login fails)
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </PageContainer>
  );
};

export default AuthPage;