
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Button, FeedItem, EventItem, Card, Icon } from '../components';
import { PageContainer } from '../components/Layout';
import { useApp } from '../AppContext';
import { PostCategory } from '../types';

const HomePage: React.FC = () => {
  const { getPosts, getEvents, getUserById } = useApp();
  const latestPosts = getPosts().slice(0, 3);
  const upcomingEvents = getEvents().slice(0, 2);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <>
      {/* Animated Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="floating-notes">
          <div className="note note-1">♪</div>
          <div className="note note-2">♫</div>
          <div className="note note-3">♪</div>
          <div className="note note-4">♬</div>
          <div className="note note-5">♩</div>
          <div className="note note-6">♪</div>
        </div>
        <div className="paint-splashes">
          <div className="splash splash-1"></div>
          <div className="splash splash-2"></div>
          <div className="splash splash-3"></div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="relative bg-gradient-to-br from-primary via-orange-500 to-secondary text-white py-32 px-4 text-center overflow-hidden">
        {/* Animated gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-blue-600/20 animate-gradient-shift"></div>

        {/* Floating geometric shapes */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="floating-shape shape-1"></div>
          <div className="floating-shape shape-2"></div>
          <div className="floating-shape shape-3"></div>
          <div className="floating-shape shape-4"></div>
        </div>

        <div className={`relative z-10 max-w-6xl mx-auto transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          <div className="mb-8 animate-bounce-slow">
            <div className="inline-block p-4 bg-white/10 rounded-full backdrop-blur-sm">
              <div className="text-6xl animate-pulse">🎨🎵</div>
            </div>
          </div>

          <h1 className="text-6xl md:text-8xl font-bold mb-6 leading-tight">
            <span className="inline-block animate-text-shimmer bg-gradient-to-r from-white via-accent to-white bg-clip-text text-transparent bg-300% animate-shimmer">
              You Love
            </span>
            <br />
            <span className="text-accent animate-pulse">MUSIC</span>
            <span className="mx-4 text-white">–</span>
            <span className="text-accent animate-pulse">ART</span>
          </h1>

          <p className="text-2xl md:text-3xl mb-12 max-w-4xl mx-auto leading-relaxed animate-fade-in-up">
            Join The MusicArt Club: A New Creative Community for Artists, Educators & Fans.
          </p>

          <div className="flex flex-col lg:flex-row justify-center items-center space-y-6 lg:space-y-0 lg:space-x-8 mb-8">
            <Button size="lg" className="bg-accent text-neutral-darkest hover:bg-accent-dark focus:ring-accent transform hover:scale-105 transition-all duration-300 shadow-2xl hover:shadow-accent/50 animate-bounce-in">
              <Link to="/auth?action=signup&role=Artist" className="flex items-center">
                <span className="mr-2">🎨</span>
                Join as an Artist
              </Link>
            </Button>
            <Button size="lg" className="bg-accent text-neutral-darkest hover:bg-accent-dark focus:ring-accent transform hover:scale-105 transition-all duration-300 shadow-2xl hover:shadow-accent/50 animate-bounce-in animation-delay-200">
              <Link to="/auth?action=signup&role=Educator" className="flex items-center">
                <span className="mr-2">🎓</span>
                Join as an Educator
              </Link>
            </Button>
          </div>

          <div className="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-6">
            <Button size="lg" variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-primary-dark transform hover:scale-105 transition-all duration-300 backdrop-blur-sm bg-white/10 animate-bounce-in animation-delay-400">
              <Link to="/explore" className="flex items-center">
                <span className="mr-2">🔍</span>
                Browse the Feed
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-primary-dark transform hover:scale-105 transition-all duration-300 backdrop-blur-sm bg-white/10 animate-bounce-in animation-delay-600">
              <Link to="/events" className="flex items-center">
                <span className="mr-2">📅</span>
                Discover Workshops
              </Link>
            </Button>
          </div>
        </div>
      </div>

      <PageContainer>
        {/* Featured Posts Section */}
        <section className="mt-20 relative">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold text-neutral-darkest mb-4 animate-fade-in-up">
              <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                Fresh from the Studio
              </span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-primary to-secondary mx-auto rounded-full animate-expand"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {latestPosts.length > 0 ? latestPosts.map((post, index) => (
              <div key={post.id} className={`animate-fade-in-up animation-delay-${index * 200}`}>
                <FeedItem post={post} author={getUserById(post.userId) || undefined} />
              </div>
            )) : (
              <div className="col-span-full text-center py-16">
                <div className="text-6xl mb-4 animate-bounce">🎨</div>
                <p className="text-xl text-neutral-dark">No posts yet. Be the first to share!</p>
              </div>
            )}
          </div>

          {latestPosts.length > 0 && (
            <div className="text-center mt-12 animate-fade-in-up animation-delay-600">
              <Button variant="primary" size="lg" className="transform hover:scale-105 transition-all duration-300 shadow-xl hover:shadow-primary/50">
                <Link to="/explore" className="flex items-center">
                  <span className="mr-2">🚀</span>
                  Explore More Art & Music
                </Link>
              </Button>
            </div>
          )}
        </section>

        {/* Upcoming Events Section */}
        <section className="mt-24 relative">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold text-neutral-darkest mb-4 animate-fade-in-up">
              <span className="bg-gradient-to-r from-secondary to-primary bg-clip-text text-transparent">
                Upcoming Events & Workshops
              </span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-secondary to-primary mx-auto rounded-full animate-expand"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {upcomingEvents.length > 0 ? upcomingEvents.map((event, index) => (
              <div key={event.id} className={`animate-fade-in-up animation-delay-${index * 200}`}>
                <EventItem event={event} educator={getUserById(event.educatorId) || undefined} />
              </div>
            )) : (
              <div className="col-span-full text-center py-16">
                <div className="text-6xl mb-4 animate-bounce">📅</div>
                <p className="text-xl text-neutral-dark">No upcoming events. Check back soon!</p>
              </div>
            )}
          </div>

          {upcomingEvents.length > 0 && (
            <div className="text-center mt-12 animate-fade-in-up animation-delay-400">
              <Button variant="primary" size="lg" className="transform hover:scale-105 transition-all duration-300 shadow-xl hover:shadow-secondary/50">
                <Link to="/events" className="flex items-center">
                  <span className="mr-2">🎪</span>
                  See All Events
                </Link>
              </Button>
            </div>
          )}
        </section>

        {/* Why Join Section */}
        <section className="mt-24 py-20 bg-gradient-to-br from-neutral-light via-white to-neutral-light rounded-3xl shadow-2xl relative overflow-hidden">
          {/* Background decoration */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute top-10 left-10 text-8xl">🎵</div>
            <div className="absolute top-20 right-20 text-6xl">🎨</div>
            <div className="absolute bottom-10 left-20 text-7xl">🎭</div>
            <div className="absolute bottom-20 right-10 text-5xl">🎪</div>
          </div>

          <div className="relative z-10">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-neutral-darkest mb-4 animate-fade-in-up">
                <span className="bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent">
                  Why Join MusicArt Club?
                </span>
              </h2>
              <div className="w-32 h-1 bg-gradient-to-r from-primary via-secondary to-accent mx-auto rounded-full animate-expand"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-10 max-w-6xl mx-auto px-6">
              <Card className="p-8 text-center hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 bg-white/80 backdrop-blur-sm border border-primary/20 animate-fade-in-up">
                <div className="mb-6 transform hover:scale-110 transition-transform duration-300">
                  <Icon name="users" className="w-16 h-16 text-primary mx-auto animate-pulse" />
                </div>
                <h3 className="text-2xl font-bold mb-4 text-neutral-darkest">Connect & Collaborate</h3>
                <p className="text-neutral-dark leading-relaxed">Meet fellow artists, educators, and fans. Find your next collaborator or student in our vibrant community.</p>
              </Card>

              <Card className="p-8 text-center hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 bg-white/80 backdrop-blur-sm border border-secondary/20 animate-fade-in-up animation-delay-200">
                <div className="mb-6 transform hover:scale-110 transition-transform duration-300">
                  <Icon name="photo" className="w-16 h-16 text-secondary mx-auto animate-pulse" />
                </div>
                <h3 className="text-2xl font-bold mb-4 text-neutral-darkest">Showcase Your Talent</h3>
                <p className="text-neutral-dark leading-relaxed">Share your music, visual art, and performances with a supportive community that celebrates creativity.</p>
              </Card>

              <Card className="p-8 text-center hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 bg-white/80 backdrop-blur-sm border border-accent/20 animate-fade-in-up animation-delay-400">
                <div className="mb-6 transform hover:scale-110 transition-transform duration-300">
                  <Icon name="academicCap" className="w-16 h-16 text-accent-dark mx-auto animate-pulse" />
                </div>
                <h3 className="text-2xl font-bold mb-4 text-neutral-darkest">Learn & Grow</h3>
                <p className="text-neutral-dark leading-relaxed">Discover workshops, lessons, and resources to expand your creative skills and artistic journey.</p>
              </Card>
            </div>
          </div>
        </section>
      </PageContainer>
    </>
  );
};

export default HomePage;
